import React, { Component } from "react";
import * as Sessions from '../../../Actions/Sessions';
import { connect } from "react-redux";
import Fire from "../../../config/Firebase";
import * as HostActions from '../../../Actions/HostAction'
import { getCookie } from '../../../Tools/helpers/domhelper'
import LoaderOverlay from "../../../components/LoaderOverlay";

class Ale extends Component {
  constructor(props) {
    super(props);
    this.UpdateConfig = this.UpdateConfig.bind(this);
    this.state = {
      updatedLink: ''
    }
  }
  componentDidMount() {
    console.log("ALE Host - ProjectDetails:", this.props.ProjectDetails);
    console.log("ALE Host - RoomId:", this.props.roomId);

    // Set a timeout to prevent infinite loading
    this.loadingTimeout = setTimeout(() => {
      if (!this.state.updatedLink) {
        console.error("ALE Host - Loading timeout reached, attempting to generate link directly");
        this.generateAleLink();
      }
    }, 10000); // 10 second timeout

    this.initializeAleLink();

    window.addEventListener('message', ({ data }) => {
      try {
        const Data = typeof data == 'string' ? JSON.parse(data) : data;
        if (Data.actiontype == "routechange") {
          this.UpdateConfig(Data.url)
        } else {
          this.props.SendCustomMessage(Data, this.props.roomId)
        }
      } catch (error) {
        console.error("ALE Host - Error processing message:", error);
      }
    });
  }

  componentWillUnmount() {
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
    }
  }

  componentDidUpdate(prevProps) {
    // Handle project switching - check if project ID changed
    const prevProjectId = prevProps.ProjectDetails?._id;
    const currentProjectId = this.props.ProjectDetails?._id;

    console.log("ALE Host - componentDidUpdate:", {
      prevProjectId,
      currentProjectId,
      projectChanged: prevProjectId !== currentProjectId
    });

    // If project ID changed, reinitialize the ALE link
    if (prevProjectId && currentProjectId && prevProjectId !== currentProjectId) {
      console.log("ALE Host - Project switch detected, reinitializing ALE...");
      this.handleProjectSwitch();
    }
  }

  handleProjectSwitch = async () => {
    console.log("ALE Host - Starting project switch sequence...");

    // Reset state to show loader
    this.setState({
      updatedLink: ''
    });

    // Clear any existing timeout
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
    }

    // Set new timeout for the new project
    this.loadingTimeout = setTimeout(() => {
      if (!this.state.updatedLink) {
        console.error("ALE Host - Loading timeout reached during project switch");
        this.generateAleLink();
      }
    }, 10000);

    try {
      // Clear the old ALE link from Firebase to signal guests
      const configRef = Fire.firestore().collection('sessions').doc(this.props.roomId).collection("config").doc("data");
      await configRef.update({
        ale_link: null,
        projectSwitching: true,
        timestamp: new Date().toISOString()
      });

      console.log("ALE Host - Cleared old ALE link, initializing new project...");

      // Wait a moment for the update to propagate
      setTimeout(() => {
        this.initializeAleLink();
      }, 500);

    } catch (error) {
      console.error("ALE Host - Error during project switch:", error);
      // Fallback to direct link generation
      this.generateAleLink();
    }
  }

  initializeAleLink = async () => {
    try {
      console.log("ALE Host - Initializing ALE link...");

      // First check if we have the required project data
      if (!this.validateProjectData()) {
        console.error("ALE Host - Invalid project data, cannot generate ALE link");
        return;
      }

      const configRef = Fire.firestore().collection('sessions').doc(this.props.roomId).collection("config").doc("data");
      const doc = await configRef.get();

      console.log("ALE Host - Config document exists:", doc.exists);

      if (doc.exists) {
        const configData = doc.data();
        console.log("ALE Host - Existing config data:", configData);

        if (configData && configData.ale_link) {
          console.log("ALE Host - Found existing ALE link:", configData.ale_link);
          this.setState({
            updatedLink: configData.ale_link
          });
          this.props.SetConfigData(configData);
          return;
        }
      }

      // No existing link found, generate new one
      console.log("ALE Host - No existing ALE link found, generating new one...");
      await this.generateAndSaveAleLink(configRef);

    } catch (error) {
      console.error("ALE Host - Error initializing ALE link:", error);
      // Try to generate link directly as fallback
      this.generateAleLink();
    }
  }

  validateProjectData = () => {
    const { ProjectDetails } = this.props;

    console.log("ALE Host - Validating project data:", {
      hasProjectDetails: !!ProjectDetails,
      projectId: ProjectDetails?._id,
      hasAleSettings: !!ProjectDetails?.projectSettings?.ale,
      initialSceneId: ProjectDetails?.projectSettings?.ale?.initial_scene_id,
      initialSceneType: ProjectDetails?.projectSettings?.ale?.initial_scene_type
    });

    if (!ProjectDetails) {
      console.error("ALE Host - No ProjectDetails available");
      return false;
    }

    if (!ProjectDetails._id) {
      console.error("ALE Host - No project ID available");
      return false;
    }

    if (!ProjectDetails.projectSettings?.ale?.initial_scene_id) {
      console.error("ALE Host - No ALE initial_scene_id in project settings");
      console.log("ALE Host - Available project settings:", ProjectDetails.projectSettings);
      return false;
    }

    console.log("ALE Host - Project data validation passed");
    return true;
  }

  generateAleLink = () => {
    if (!this.validateProjectData()) {
      console.error("ALE Host - Cannot generate ALE link due to missing project data");
      return null;
    }

    let organization = getCookie("organization");
    console.log("ALE Host - Organization from cookie (raw):", organization);

    // Handle case where organization might be stored as JSON or object
    if (!organization) {
      console.error("ALE Host - No organization cookie found");
      // Try to get organization from project details
      if (this.props.ProjectDetails?.organization_id) {
        organization = this.props.ProjectDetails.organization_id;
        console.log("ALE Host - Using organization from ProjectDetails:", organization);
      } else {
        console.error("ALE Host - No organization found in ProjectDetails either");
        return null;
      }
    }

    // If organization is a JSON string, parse it
    if (typeof organization === 'string' && organization.startsWith('[')) {
      try {
        const orgArray = JSON.parse(organization);
        organization = orgArray[0]; // Take the first organization
        console.log("ALE Host - Parsed organization from array:", organization);
      } catch (e) {
        console.error("ALE Host - Failed to parse organization JSON:", e);
      }
    }

    const { ProjectDetails } = this.props;
    const sceneType = ProjectDetails.projectSettings.ale?.initial_scene_type === "project"
      ? "projectscene"
      : "masterscene";

    const ale_link = `${process.env.REACT_APP_API_PROPVR_UI_LIBRARY}${organization}/${sceneType}/${ProjectDetails._id}/${ProjectDetails.projectSettings.ale.initial_scene_id}`;

    console.log("ALE Host - Generated ALE link:", ale_link);

    this.setState({
      updatedLink: ale_link
    });

    return ale_link;
  }

  generateAndSaveAleLink = async (configRef) => {
    const ale_link = this.generateAleLink();

    if (!ale_link) {
      console.error("ALE Host - Failed to generate ALE link");
      return;
    }

    try {
      console.log("ALE Host - Saving ALE link to Firebase:", ale_link);
      await configRef.set({
        ale_link: ale_link,
        roomId: this.props.roomId,
        projectSwitching: false, // Clear the project switching flag
        timestamp: new Date().toISOString()
      });

      const configData = {
        ale_link,
        roomId: this.props.roomId,
        projectSwitching: false
      };
      this.props.SetConfigData(configData);
      console.log("ALE Host - Successfully saved ALE link to Firebase");

    } catch (error) {
      console.error("ALE Host - Error saving ALE link to Firebase:", error);
      // Even if Firebase save fails, we can still use the link locally
    }
  }
  UpdateConfig(url) {
    console.log("ALE Host - Updating config with new URL:", url);
    Fire.firestore().collection("sessions").doc(this.props.roomId).collection("config").doc("data").update({
      ale_link: url,
      projectSwitching: false, // Ensure project switching flag is cleared
      timestamp: new Date().toISOString()
    }).then(() => {
      console.log("ALE Host - Successfully updated config with new URL");
      this.setState({
        updatedLink: url
      });
    }).catch(error => {
      console.error("ALE Host - Error updating config:", error);
    })
  }

  render() {
    const { updatedLink } = this.state;
    console.log("ALE Host - Rendering with updatedLink:", updatedLink);

    return updatedLink ? (
      <iframe
        style={{ width: "100%", height: "100%", position: "absolute" }}
        src={updatedLink}
        onLoad={() => console.log("ALE Host - Iframe loaded successfully")}
        onError={() => console.error("ALE Host - Iframe failed to load")}
      />
    ) : (
      <LoaderOverlay
        title="Preparing Your Virtual Experience"
        message="Just a moment while we set the stage for your immersive journey..."
      />
    );
  }
}

const mapStateToProps = state => {
  return {
    Config: state.Call.config,
    configDetails: state.Sessions.configData,
    ProjectDetails: state.Sessions.projectDetails
  }
}
const mapDispatchToProps = {
  ...Sessions,
  ...HostActions
}

export default connect(mapStateToProps, mapDispatchToProps)(Ale);
